import os
import time
import json
import logging
import base64
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from io import BytesIO

import pyautogui
import cv2
import numpy as np
from PIL import Image
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.getenv('LOG_FILE', 'agent.log')),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

@dataclass
class ActionResult:
    """Result of an action execution"""
    success: bool
    message: str
    coordinates: Optional[Tuple[int, int]] = None
    screenshot_path: Optional[str] = None
    error: Optional[str] = None

@dataclass
class TaskStep:
    """Individual step in a task"""
    description: str
    action_type: str  # 'click', 'type', 'scroll', 'wait', 'analyze'
    coordinates: Optional[Tuple[int, int]] = None
    text: Optional[str] = None
    completed: bool = False

class ScreenAIAgent:
    """
    AI Agent that can interact with screen using Gemini 2.5 Flash
    """

    def __init__(self, api_key: Optional[str] = None):
        """Initialize the AI Agent"""
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("Gemini API key is required. Set GEMINI_API_KEY environment variable.")

        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')

        # Configuration
        self.screenshot_delay = float(os.getenv('SCREENSHOT_DELAY', '1.0'))
        self.click_delay = float(os.getenv('CLICK_DELAY', '0.5'))
        self.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        self.safety_mode = os.getenv('SAFETY_MODE', 'true').lower() == 'true'

        # Disable pyautogui failsafe for automation
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = self.click_delay

        # Task tracking
        self.current_task: Optional[str] = None
        self.task_steps: List[TaskStep] = []
        self.execution_history: List[ActionResult] = []

        logger.info("ScreenAIAgent initialized successfully")

    def capture_screenshot(self, save_path: Optional[str] = None) -> str:
        """
        Capture a screenshot of the current screen

        Args:
            save_path: Optional path to save the screenshot

        Returns:
            Path to the saved screenshot
        """
        try:
            if save_path is None:
                save_path = f"screenshot_{int(time.time())}.png"

            screenshot = pyautogui.screenshot()
            screenshot.save(save_path)

            logger.info(f"Screenshot captured and saved to {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"Failed to capture screenshot: {e}")
            raise

    def encode_image_to_base64(self, image_path: str) -> str:
        """
        Encode image to base64 for API transmission

        Args:
            image_path: Path to the image file

        Returns:
            Base64 encoded image string
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to encode image: {e}")
            raise

    def analyze_screen(self, screenshot_path: str, task_description: str) -> Dict[str, Any]:
        """
        Analyze screenshot using Gemini 2.5 Flash and get actionable instructions

        Args:
            screenshot_path: Path to the screenshot
            task_description: Description of what needs to be accomplished

        Returns:
            Dictionary containing analysis results and next actions
        """
        try:
            # Read the image
            with open(screenshot_path, 'rb') as image_file:
                image_data = image_file.read()

            # Create the prompt for Gemini
            prompt = f"""
            You are an AI assistant that helps automate computer tasks by analyzing screenshots.

            Current Task: {task_description}

            Please analyze this screenshot and provide the next action to take. Your response should be in JSON format with the following structure:

            {{
                "analysis": "Brief description of what you see on the screen",
                "next_action": {{
                    "type": "click|type|scroll|wait|complete",
                    "description": "What this action will accomplish",
                    "coordinates": [x, y] (for click actions, provide pixel coordinates),
                    "text": "text to type" (for type actions),
                    "scroll_direction": "up|down|left|right" (for scroll actions),
                    "wait_time": seconds (for wait actions)
                }},
                "confidence": 0.0-1.0,
                "reasoning": "Explanation of why this action was chosen",
                "task_progress": "What has been accomplished so far",
                "is_task_complete": true/false
            }}

            Important guidelines:
            1. Provide exact pixel coordinates for click actions
            2. Be specific about what elements you're targeting
            3. If the task appears complete, set "is_task_complete" to true
            4. If you need to wait for something to load, use "wait" action
            5. Be conservative with coordinates - aim for the center of clickable elements
            6. Consider the current state and what the user is trying to accomplish
            """

            # Send to Gemini
            response = self.model.generate_content([
                prompt,
                {
                    "mime_type": "image/png",
                    "data": image_data
                }
            ])

            # Parse the response
            try:
                result = json.loads(response.text)
                logger.info(f"Screen analysis completed: {result.get('analysis', 'No analysis')}")
                return result
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract useful information
                logger.warning("Failed to parse JSON response, attempting text extraction")
                return {
                    "analysis": response.text,
                    "next_action": {"type": "wait", "description": "Analyze response manually", "wait_time": 1},
                    "confidence": 0.5,
                    "reasoning": "Could not parse structured response",
                    "task_progress": "Unknown",
                    "is_task_complete": False
                }

        except Exception as e:
            logger.error(f"Failed to analyze screen: {e}")
            raise

    def get_screen_info(self) -> Dict[str, int]:
        """Get screen dimensions and other info"""
        screen_width, screen_height = pyautogui.size()
        return {
            "width": screen_width,
            "height": screen_height,
            "center_x": screen_width // 2,
            "center_y": screen_height // 2
        }

    def validate_coordinates(self, x: int, y: int) -> bool:
        """
        Validate that coordinates are within screen bounds

        Args:
            x: X coordinate
            y: Y coordinate

        Returns:
            True if coordinates are valid
        """
        screen_info = self.get_screen_info()
        return (0 <= x <= screen_info["width"] and
                0 <= y <= screen_info["height"])

    def safe_click(self, x: int, y: int, button: str = 'left', clicks: int = 1) -> ActionResult:
        """
        Safely click at specified coordinates with validation

        Args:
            x: X coordinate
            y: Y coordinate
            button: Mouse button ('left', 'right', 'middle')
            clicks: Number of clicks

        Returns:
            ActionResult with success status and details
        """
        try:
            # Validate coordinates
            if not self.validate_coordinates(x, y):
                return ActionResult(
                    success=False,
                    message=f"Invalid coordinates: ({x}, {y})",
                    error="Coordinates out of screen bounds"
                )

            # Safety check in safety mode
            if self.safety_mode:
                logger.info(f"Safety mode: Would click at ({x}, {y}) with {button} button")
                user_input = input(f"Confirm click at ({x}, {y})? (y/n): ").lower()
                if user_input != 'y':
                    return ActionResult(
                        success=False,
                        message="Click cancelled by user",
                        coordinates=(x, y)
                    )

            # Perform the click
            pyautogui.click(x, y, clicks=clicks, button=button)
            time.sleep(self.click_delay)

            logger.info(f"Successfully clicked at ({x}, {y})")
            return ActionResult(
                success=True,
                message=f"Clicked at ({x}, {y})",
                coordinates=(x, y)
            )

        except Exception as e:
            logger.error(f"Failed to click at ({x}, {y}): {e}")
            return ActionResult(
                success=False,
                message=f"Click failed: {e}",
                coordinates=(x, y),
                error=str(e)
            )

    def safe_type(self, text: str, interval: float = 0.1) -> ActionResult:
        """
        Safely type text with specified interval between characters

        Args:
            text: Text to type
            interval: Interval between characters

        Returns:
            ActionResult with success status
        """
        try:
            if self.safety_mode:
                logger.info(f"Safety mode: Would type text: '{text}'")
                user_input = input(f"Confirm typing '{text}'? (y/n): ").lower()
                if user_input != 'y':
                    return ActionResult(
                        success=False,
                        message="Typing cancelled by user",
                        text=text
                    )

            pyautogui.typewrite(text, interval=interval)
            time.sleep(self.click_delay)

            logger.info(f"Successfully typed: '{text}'")
            return ActionResult(
                success=True,
                message=f"Typed: '{text}'",
                text=text
            )

        except Exception as e:
            logger.error(f"Failed to type text: {e}")
            return ActionResult(
                success=False,
                message=f"Typing failed: {e}",
                text=text,
                error=str(e)
            )

    def safe_scroll(self, direction: str, amount: int = 3) -> ActionResult:
        """
        Safely scroll in specified direction

        Args:
            direction: Scroll direction ('up', 'down', 'left', 'right')
            amount: Amount to scroll

        Returns:
            ActionResult with success status
        """
        try:
            if direction.lower() in ['up', 'down']:
                scroll_amount = amount if direction.lower() == 'up' else -amount
                pyautogui.scroll(scroll_amount)
            else:
                # For horizontal scrolling, we might need to use keyboard shortcuts
                if direction.lower() == 'left':
                    pyautogui.keyDown('shift')
                    pyautogui.scroll(amount)
                    pyautogui.keyUp('shift')
                elif direction.lower() == 'right':
                    pyautogui.keyDown('shift')
                    pyautogui.scroll(-amount)
                    pyautogui.keyUp('shift')

            time.sleep(self.click_delay)

            logger.info(f"Successfully scrolled {direction} by {amount}")
            return ActionResult(
                success=True,
                message=f"Scrolled {direction} by {amount}"
            )

        except Exception as e:
            logger.error(f"Failed to scroll: {e}")
            return ActionResult(
                success=False,
                message=f"Scroll failed: {e}",
                error=str(e)
            )

    def execute_action(self, action_data: Dict[str, Any]) -> ActionResult:
        """
        Execute a single action based on AI analysis

        Args:
            action_data: Dictionary containing action details from AI analysis

        Returns:
            ActionResult with execution status
        """
        action_type = action_data.get('type', '').lower()

        try:
            if action_type == 'click':
                coordinates = action_data.get('coordinates', [])
                if len(coordinates) >= 2:
                    return self.safe_click(coordinates[0], coordinates[1])
                else:
                    return ActionResult(
                        success=False,
                        message="Invalid coordinates for click action",
                        error="Missing or invalid coordinates"
                    )

            elif action_type == 'type':
                text = action_data.get('text', '')
                if text:
                    return self.safe_type(text)
                else:
                    return ActionResult(
                        success=False,
                        message="No text provided for type action",
                        error="Missing text"
                    )

            elif action_type == 'scroll':
                direction = action_data.get('scroll_direction', 'down')
                return self.safe_scroll(direction)

            elif action_type == 'wait':
                wait_time = action_data.get('wait_time', 1)
                time.sleep(wait_time)
                return ActionResult(
                    success=True,
                    message=f"Waited for {wait_time} seconds"
                )

            elif action_type == 'complete':
                return ActionResult(
                    success=True,
                    message="Task marked as complete"
                )

            else:
                return ActionResult(
                    success=False,
                    message=f"Unknown action type: {action_type}",
                    error="Unsupported action type"
                )

        except Exception as e:
            logger.error(f"Failed to execute action {action_type}: {e}")
            return ActionResult(
                success=False,
                message=f"Action execution failed: {e}",
                error=str(e)
            )

    def execute_task(self, task_description: str, max_steps: int = 20) -> List[ActionResult]:
        """
        Execute a complete task by analyzing screen and taking actions step by step

        Args:
            task_description: Description of the task to accomplish
            max_steps: Maximum number of steps to prevent infinite loops

        Returns:
            List of ActionResults for each step taken
        """
        self.current_task = task_description
        self.execution_history = []

        logger.info(f"Starting task execution: {task_description}")

        for step in range(max_steps):
            try:
                # Capture current screen
                screenshot_path = self.capture_screenshot()

                # Analyze screen and get next action
                analysis = self.analyze_screen(screenshot_path, task_description)

                # Check if task is complete
                if analysis.get('is_task_complete', False):
                    logger.info("Task completed successfully")
                    result = ActionResult(
                        success=True,
                        message="Task completed",
                        screenshot_path=screenshot_path
                    )
                    self.execution_history.append(result)
                    break

                # Execute the next action
                next_action = analysis.get('next_action', {})
                if not next_action:
                    logger.warning("No next action provided by AI")
                    result = ActionResult(
                        success=False,
                        message="No action provided by AI",
                        screenshot_path=screenshot_path,
                        error="Missing action in AI response"
                    )
                    self.execution_history.append(result)
                    break

                # Execute the action
                result = self.execute_action(next_action)
                result.screenshot_path = screenshot_path
                self.execution_history.append(result)

                # Log progress
                logger.info(f"Step {step + 1}: {result.message}")

                # If action failed, try to continue or break based on severity
                if not result.success:
                    logger.warning(f"Action failed: {result.error}")
                    # For now, continue to next step, but could implement retry logic

                # Wait before next iteration
                time.sleep(self.screenshot_delay)

            except Exception as e:
                logger.error(f"Error in step {step + 1}: {e}")
                result = ActionResult(
                    success=False,
                    message=f"Step {step + 1} failed: {e}",
                    error=str(e)
                )
                self.execution_history.append(result)
                break

        else:
            # Max steps reached
            logger.warning(f"Task execution stopped after {max_steps} steps")
            result = ActionResult(
                success=False,
                message=f"Task execution stopped after {max_steps} steps",
                error="Maximum steps reached"
            )
            self.execution_history.append(result)

        return self.execution_history

    def get_task_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current task execution

        Returns:
            Dictionary containing task summary
        """
        if not self.execution_history:
            return {"status": "No task executed"}

        successful_steps = sum(1 for result in self.execution_history if result.success)
        total_steps = len(self.execution_history)

        return {
            "task": self.current_task,
            "total_steps": total_steps,
            "successful_steps": successful_steps,
            "success_rate": successful_steps / total_steps if total_steps > 0 else 0,
            "last_action": self.execution_history[-1].message if self.execution_history else "None",
            "completed": any("completed" in result.message.lower() for result in self.execution_history)
        }

    def emergency_stop(self):
        """Emergency stop function to halt all operations"""
        logger.critical("EMERGENCY STOP ACTIVATED")
        self.current_task = None
        # Could add more emergency procedures here

    def set_safety_mode(self, enabled: bool):
        """Enable or disable safety mode"""
        self.safety_mode = enabled
        logger.info(f"Safety mode {'enabled' if enabled else 'disabled'}")

    def add_safety_check(self, action_type: str, coordinates: Tuple[int, int] = None) -> bool:
        """
        Additional safety checks for critical actions

        Args:
            action_type: Type of action being performed
            coordinates: Coordinates for click actions

        Returns:
            True if action is safe to proceed
        """
        # Check for dangerous coordinates (e.g., system areas)
        if coordinates:
            x, y = coordinates
            screen_info = self.get_screen_info()

            # Avoid clicking in system areas (taskbar, etc.)
            if y > screen_info["height"] - 50:  # Bottom taskbar area
                logger.warning(f"Attempted click in taskbar area: ({x}, {y})")
                return False

            if x < 50 and y < 50:  # Top-left system area
                logger.warning(f"Attempted click in system area: ({x}, {y})")
                return False

        # Add more safety checks as needed
        return True

    def retry_action(self, action_data: Dict[str, Any], max_retries: int = None) -> ActionResult:
        """
        Retry an action with exponential backoff

        Args:
            action_data: Action to retry
            max_retries: Maximum number of retries (uses self.max_retries if None)

        Returns:
            ActionResult from the final attempt
        """
        if max_retries is None:
            max_retries = self.max_retries

        last_result = None

        for attempt in range(max_retries + 1):
            try:
                result = self.execute_action(action_data)

                if result.success:
                    if attempt > 0:
                        logger.info(f"Action succeeded on attempt {attempt + 1}")
                    return result

                last_result = result

                if attempt < max_retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.info(f"Action failed, retrying in {wait_time} seconds (attempt {attempt + 1}/{max_retries + 1})")
                    time.sleep(wait_time)

            except Exception as e:
                last_result = ActionResult(
                    success=False,
                    message=f"Retry attempt {attempt + 1} failed: {e}",
                    error=str(e)
                )

                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"Exception in attempt {attempt + 1}, retrying in {wait_time} seconds")
                    time.sleep(wait_time)

        logger.error(f"Action failed after {max_retries + 1} attempts")
        return last_result or ActionResult(
            success=False,
            message="All retry attempts failed",
            error="Maximum retries exceeded"
        )


def main():
    """
    Main function to demonstrate the AI Agent usage
    """
    print("🤖 Screen AI Agent - Powered by Gemini 2.5 Flash")
    print("=" * 50)

    # Check for API key
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ Error: GEMINI_API_KEY environment variable not set")
        print("Please set your Gemini API key in a .env file or environment variable")
        return

    try:
        # Initialize the agent
        agent = ScreenAIAgent()

        # Interactive mode
        while True:
            print("\n🎯 What would you like me to do?")
            print("Examples:")
            print("- 'Open notepad and type hello world'")
            print("- 'Click on the start menu'")
            print("- 'Find and click the Chrome icon'")
            print("- 'type: quit' to exit")

            # task = input("\n📝 Enter your task: ").strip()
            task = "Click on windows button"

            if task.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            if not task:
                print("❌ Please enter a task description")
                continue

            print(f"\n🚀 Starting task: {task}")
            print("⚠️  Make sure your screen is ready and visible")

            # Ask for confirmation
            confirm = input("Ready to proceed? (y/n): ").lower()
            if confirm != 'y':
                print("Task cancelled")
                continue

            # Execute the task
            try:
                results = agent.execute_task(task)

                # Show summary
                summary = agent.get_task_summary()
                print(f"\n📊 Task Summary:")
                print(f"   Task: {summary['task']}")
                print(f"   Steps: {summary['successful_steps']}/{summary['total_steps']}")
                print(f"   Success Rate: {summary['success_rate']:.1%}")
                print(f"   Status: {'✅ Completed' if summary['completed'] else '❌ Incomplete'}")

                # Show last few actions
                print(f"\n📋 Recent Actions:")
                for result in results[-3:]:
                    status = "✅" if result.success else "❌"
                    print(f"   {status} {result.message}")

            except KeyboardInterrupt:
                print("\n⚠️  Task interrupted by user")
                agent.emergency_stop()
            except Exception as e:
                print(f"\n❌ Error executing task: {e}")
                logger.error(f"Task execution error: {e}")

    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        logger.error(f"Agent initialization error: {e}")


if __name__ == "__main__":
    main()