#!/usr/bin/env python3
"""
Quick test for the improved AI agent
"""

import os
from dotenv import load_dotenv
from agent import ScreenAIAgent

def test_json_parsing():
    """Test the improved JSON parsing"""
    print("🧪 Testing improved JSON parsing...")
    
    load_dotenv()
    
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ No API key found")
        return
    
    try:
        agent = ScreenAIAgent()
        agent.set_safety_mode(True)
        
        # Test screenshot and analysis
        screenshot_path = agent.capture_screenshot("test_json.png")
        analysis = agent.analyze_screen(screenshot_path, "Find the Windows button and click it")
        
        print(f"✅ Analysis successful!")
        print(f"🔍 Analysis: {analysis.get('analysis', 'No analysis')}")
        print(f"🎯 Next action: {analysis.get('next_action', {}).get('type', 'None')}")
        print(f"📍 Coordinates: {analysis.get('next_action', {}).get('coordinates', 'None')}")
        print(f"🎯 Confidence: {analysis.get('confidence', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_windows_button_task():
    """Test the Windows button clicking task with verbose output"""
    print("\n🎯 Testing Windows button task with verbose output...")
    
    try:
        agent = ScreenAIAgent()
        agent.set_safety_mode(False)  # Disable for automated test
        
        # Execute the task with max 2 steps for testing
        results = agent.execute_task("Click on the Windows button", max_steps=2)
        
        print(f"\n📊 Test Results:")
        summary = agent.get_task_summary()
        print(f"   Steps: {summary['successful_steps']}/{summary['total_steps']}")
        print(f"   Success Rate: {summary['success_rate']:.1%}")
        
        return len(results) > 0 and any(r.success for r in results)
        
    except Exception as e:
        print(f"❌ Windows button test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Quick Test Suite for Improved AI Agent")
    print("=" * 50)
    
    # Test 1: JSON parsing
    test1_passed = test_json_parsing()
    
    # Test 2: Windows button task
    test2_passed = test_windows_button_task()
    
    # Results
    passed = sum([test1_passed, test2_passed])
    total = 2
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The improvements are working.")
    else:
        print("⚠️  Some tests failed. Check the output above.")
